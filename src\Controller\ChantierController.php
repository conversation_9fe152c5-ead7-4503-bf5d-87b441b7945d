<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use App\Entity\Chantier;
use App\Entity\Artisan;
use App\Entity\Carrelage;
use App\Entity\Chappe;
use App\Entity\Charpente;
use App\Entity\Enduit;
use App\Entity\Escalier;
use App\Entity\Etancheite;
use App\Entity\Finition;
use App\Entity\LaineSouffle;
use App\Entity\Maconnerie;
use App\Entity\Menuiserie;
use App\Entity\Peinture;
use App\Entity\Permeabilite;
use App\Entity\Placo;
use App\Entity\PlomberieElec;
use App\Entity\Terrassement;
use App\Entity\Zinguerie;
use App\Entity\EntretientPAC;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Doctrine\ORM\EntityManagerInterface;
use App\Repository\ChantierRepository;
use App\Repository\ArtisanRepository;
use App\Form\ChantierType;


class ChantierController extends AbstractController
{

    #[Route('/', name: 'app_home')]
    public function home(): Response
    {
        return $this->redirectToRoute('app_login');
    }

    const ordre_chantier = [
        ['key' => 'Fondation', 'value' => 'EtatFondation'],
        ['key' => 'Dalle', 'value' => 'EtatDalle'],
        ['key' => 'PrepaDalle', 'value' => 'EtatPrepaDalle'],
        ['key' => 'Branchement', 'value' => 'EtatBranchement'],
        ['key' => 'Elevation', 'value' => 'EtatElevation'],
        ['key' => 'Rampannage', 'value' => 'EtatRampannage'],
        ['key' => 'ReleveSeuil', 'value' => 'EtatReleveSeuil'],
        ['key' => 'Seuil', 'value' => 'EtatSeuil'],
        ['key' => 'Charpentes', 'value' => 'EtatCharpente'],
        ['key' => 'Couverture', 'value' => 'EtatCouverture'],
        // optionel
        ['key' => 'EtancheiteIntervention', 'value' => 'EtatEtancheiteIntervention'],
        ['key' => 'Couvertine', 'value' => 'EtatCouvertine'],
        ['key' => 'Goutiere', 'value' => 'EtatGoutiere'],
        ['key' => 'PoseMenuiserie', 'value' => 'EtatPoseMenuiserie'],
        ['key' => 'PrepaPlaco', 'value' => 'EtatPrepaPlaco'],
        ['key' => 'PrepaFileriePlomberie', 'value' => 'EtatPrepaFileriePlomberie'],
        ['key' => 'FinPlaco', 'value' => 'EtatFinPlaco'],
        ['key' => 'GrosOeuvre', 'value' => 'EtatGrosOeuvre'],
        ['key' => 'FinitionElec', 'value' => 'EtatFinitionElec'],
        ['key' => 'ChappeIntervention', 'value' => 'EtatChappeIntervention'],
        ['key' => 'PosePACChaudiere', 'value' => 'EtatPosePACChaudiere'],
        ['key' => 'MES', 'value' => 'EtatMES'],
        ['key' => 'Ravalement', 'value' => 'EtatRavalement'],
        ['key' => 'Descente', 'value' => 'EtatDescente'],
        // optionel 
        ['key' => 'LaineSouffleIntervention', 'value' => 'EtatLaineSouffleIntervention'],
        ['key' => 'PoseCarrelage', 'value' => 'EtatPoseCarrelage'],
        // optionel
        ['key' => 'PoseEscalier', 'value' => 'EtatPoseEscalier'],
        // optionel
        ['key' => 'Cloison', 'value' => 'EtatCloison'],
        ['key' => 'FinitionPlomberieSanitaire', 'value' => 'EtatFinitionPlomberieSanitaire'],
        ['key' => 'Finitions', 'value' => 'EtatFinition'],
        ['key' => 'PosePlinthe', 'value' => 'EtatPosePlinthe'],
        ['key' => 'PeintureIntervention', 'value' => 'EtatPeinture'],
        ['key' => 'PermeabiliteIntervention', 'value' => 'EtatPermeabiliteIntervention'],
    ];

    const findetat = [
        'Fondation' => 'EtatFondation',
        'Dalle' => 'EtatDalle',
        'PrepaDalle' => 'EtatPrepaDalle',
        'Branchement' => 'EtatBranchement',
        'Elevation' => 'EtatElevation',
        'Rampannage' => 'EtatRampannage',
        'ReleveSeuil' => 'EtatReleveSeuil',
        'Seuil' => 'EtatSeuil',
        'Charpentes' => 'EtatCharpente',
        'Couverture' => 'EtatCouverture',
        'EtancheiteIntervention' => 'EtatEtancheiteIntervention',
        'Couvertine' => 'EtatCouvertine',
        'Goutiere' => 'EtatGoutiere',
        'PoseMenuiserie' => 'EtatPoseMenuiserie',
        'PrepaPlaco' => 'EtatPrepaPlaco',
        'PrepaFileriePlomberie' => 'EtatPrepaFileriePlomberie',
        'FinPlaco' => 'EtatFinPlaco',
        'GrosOeuvre' => 'EtatGrosOeuvre',
        'FinitionElec' => 'EtatFinitionElec',
        'ChappeIntervention' => 'EtatChappeIntervention',
        'PosePACChaudiere' => 'EtatPosePACChaudiere',
        'MES' => 'EtatMES',
        'Ravalement' => 'EtatRavalement',
        'Descente' => 'EtatDescente',
        'LaineSouffleIntervention' => 'EtatLaineSouffleIntervention',
        'PoseCarrelage' => 'EtatPoseCarrelage',
        'PoseEscalier' => 'EtatPoseEscalier',
        'Cloison' => 'EtatCloison',
        'FinitionPlomberieSanitaire' => 'EtatFinitionPlomberieSanitaire',
        'Finitions' => 'EtatFinition',
        'PosePlinthe' => 'EtatPosePlinthe',
        'PeintureIntervention' => 'EtatPeinture',
        'PermeabiliteIntervention' => 'EtatPermeabiliteIntervention',
    ];

    const names = [
        "Fondation" => "Fondation",
        "Dalle" => "Dalle",
        "PrepaDalle" => "Préparation dalle",
        "Branchement" => "Branchement",
        "Elevation" => "Fin élévation",
        "Rampannage" => "Rampannage",
        "ReleveSeuil" => "Relevés de seuils",
        "Seuil" => "Seuils",
        "Charpentes" => "Charpentes",
        "Couverture" => "Intervention charpente - couverture",
        "EtancheiteIntervention" => "Étanchéité intervention",
        "Couvertine" => "Pose couvertines",
        "Goutiere" => "Pose gouttière",
        "PoseMenuiserie" => "Pose menuiseries",
        "PrepaPlaco" => "Préparation placo",
        "PrepaFileriePlomberie" => "Préparation filerie et plomberie",
        "FinPlaco" => "Fin placo",
        "FinitionElec" => "Finition électricité",
        "GrosOeuvre" => "Gros œuvre",
        "ChappeIntervention" => "Chape liquide",
        "PosePACChaudiere" => "Pose PAC et BAC",
        "MES" => "MES",
        "Ravalement" => "Ravalement",
        "Descente" => "Pose descente",
        "LaineSouffleIntervention" => "Isolation soufflée",
        "PoseCarrelage" => "Pose carrelage",
        "PoseEscalier" => "Pose escalier",
        "Cloison" => "Cloison après carrelage",
        "FinitionPlomberieSanitaire" => "Finition plomberie + sanitaire",
        "Finitions" => "Finitions intérieures",
        "PosePlinthe" => "Pose plinthes",
        "PeintureIntervention" => "Peinture",
        "PermeabiliteIntervention" => "Perméa"
    ];

    const num_semaine = [
        "Fondation" => 0,
        "Dalle" => 3,
        "PrepaDalle" => 3, // Correspond à 'Préparation dalle'
        "Branchement" => 4,
        "Elevation" => 7, // Correspond à 'Fin élèvation'
        "Rampannage" => 8,
        "ReleveSeuil" => 8, // Correspond à 'Relevés de seuils'
        "Seuil" => 12, // Correspond à 'Seuils'
        'Charpentes' => 9,
        "Couverture" => 9, // Correspond à 'Intervention charpente - couverture'
        "EtancheiteIntervention" => 10,
        "Couvertine" => 11, // Correspond à 'Pose couvertines'
        "Goutiere" => 11, // Correspond à 'Pose gouttiére'
        "PoseMenuiserie" => 11, // Correspond à 'Pose menuiseries'
        "PrepaPlaco" => 12, // Correspond à 'Préparation placo'
        "PrepaFileriePlomberie" => 13, // Correspond à 'Prépa Plomberie' et 'Préparation filerie'
        "FinPlaco" => 15,
        "FinitionElec" => 16, // Correspond à 'Finition electricité'
        "GrosOeuvre" => 17,
        "ChappeIntervention" => 18, // Correspond à 'Chape liquide'
        "PosePACChaudiere" => 19, // Correspond à 'Pose PAC et BAC'
        "MES" => 20,
        "Ravalement" => 21,
        "Descente" => 22, // Correspond à 'Pose descente'
        "LaineSouffleIntervention" => 22, // Correspond à 'ISOLATION Soufflée'
        "PoseCarrelage" => 23,
        "PoseEscalier" => 25,
        "Cloison" => 27, // Correspond à 'Cloison après carrelage'
        "FinitionPlomberieSanitaire" => 24, // Correspond à 'Finition Plomberie + Sanitaire'
        "Finitions" => 25, // Correspond à 'Finitions Intérieures'
        "PosePlinthe" => 27,
        "PeintureIntervention" => 27, // Correspond à 'Peinture'
        "PermeabiliteIntervention" => 27, // Correspond à 'Perméa'
    ];


    #[Route('/chantier', name: 'app_chantier')]
    public function index(
        ChantierRepository $chantierRepository,
        ArtisanRepository $artisanRepository
        ): Response
    {


        $artisans = $artisanRepository->findAll();
        $chantiers = $chantierRepository->findBy([], ['id' => 'DESC']);
        $cdt = $chantierRepository->findAllCDTDistinct();

        $artisans_optgroup = [];
        foreach ($artisans as $artisan) {
            if (!array_key_exists($artisan->getType(), $artisans_optgroup)) {
                $artisans_optgroup[$artisan->getType()] = [];
            }
            $artisans_optgroup[$artisan->getType()][] = $artisan;
        }
        foreach ($artisans_optgroup as $key => $value) {
            usort($artisans_optgroup[$key], function($a, $b) {
                return $a->getNom() <=> $b->getNom();
            });
        }

        return $this->render('chantier/index.html.twig', [
            'chantiers' => $chantiers,
            'artisans' => $artisans,
            'artisans_optgroup' => $artisans_optgroup,
            'cdts' => $cdt
        ]);
    }

    #[Route('/chantier/new', name: 'app_chantier_new')]
    public function new(Request $request, EntityManagerInterface $em,
    ArtisanRepository $artisanRepository,
    ChantierRepository $chantierRepository
    ): Response
    {
        $chantier = new Chantier();
        $cdt = $chantierRepository->findAllCDTDistinct();
        $form = $this->createForm(ChantierType::class, $chantier);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em->persist($chantier);

            $dateDeb = $chantier->getDateDeb();
            $dateDeb_week = $dateDeb->format('W');
            $artisants = $request->request->all()['chantier'];

            $carrelage = new Carrelage();
            $em->persist($carrelage);
            $carrelage->setChantier($chantier);
            if(isset($artisants['Carrelage'])){
                $artisants['Carrelage'] = $artisanRepository->find($artisants['Carrelage']);
                $carrelage->setArtisan($artisants['Carrelage'] ?? null);
            }

            $chappe = new Chappe();
            $em->persist($chappe);
            $chappe->setChantier($chantier);
            if(isset($artisants['Chappe'])){
                $artisants['Chappe'] = $artisanRepository->find($artisants['Chappe']);
                $chappe->setArtisan($artisants['Chappe'] ?? null);
            }

            $charpente = new Charpente();
            $em->persist($charpente);
            $charpente->setChantier($chantier);
            if(isset($artisants['Charpente'])){
                $artisants['Charpente'] = $artisanRepository->find($artisants['Charpente']);
                $charpente->setArtisan($artisants['Charpente'] ?? null);
            }

            $enduit = new Enduit();
            $em->persist($enduit);
            $enduit->setChantier($chantier);
            if(isset($artisants['Enduit'])){
                $artisants['Enduit'] = $artisanRepository->find($artisants['Enduit']);
                $enduit->setArtisan($artisants['Enduit'] ?? null);
            }

            $escalier = new Escalier();
            $em->persist($escalier);
            $escalier->setChantier($chantier);
            if(isset($artisants['Escalier'])){
                $artisants['Escalier'] = $artisanRepository->find($artisants['Escalier']);
                $escalier->setArtisan($artisants['Escalier'] ?? null);
            }

            $etancheite = new Etancheite();
            $em->persist($etancheite);
            $etancheite->setChantier($chantier);
            if(isset($artisants['Etancheite'])){
                $artisants['Etancheite'] = $artisanRepository->find($artisants['Etancheite']);
                $etancheite->setArtisan($artisants['Etancheite'] ?? null);
            }

            $finition = new Finition();
            $em->persist($finition);
            $finition->setChantier($chantier);
            if(isset($artisants['Finition'])){
                $artisants['Finition'] = $artisanRepository->find($artisants['Finition']);
                $finition->setArtisan($artisants['Finition'] ?? null);
            }

            $laineSouffle = new LaineSouffle();
            $em->persist($laineSouffle);
            $laineSouffle->setChantier($chantier);
            if(isset($artisants['LaineSouffle'])){
                $artisants['LaineSouffle'] = $artisanRepository->find($artisants['LaineSouffle']);
                $laineSouffle->setArtisan($artisants['LaineSouffle'] ?? null);
            }

            $maconnerie = new Maconnerie();
            $em->persist($maconnerie);
            $maconnerie->setChantier($chantier);
            if(isset($artisants['Maconnerie'])){
                $artisants['Maconnerie'] = $artisanRepository->find($artisants['Maconnerie']);
                $maconnerie->setArtisan($artisants['Maconnerie'] ?? null);
            }

            $menuiserie = new Menuiserie();
            $em->persist($menuiserie);
            $menuiserie->setChantier($chantier);
            if(isset($artisants['Menuiserie'])){
                $artisants['Menuiserie'] = $artisanRepository->find($artisants['Menuiserie']);
                $menuiserie->setArtisan($artisants['Menuiserie'] ?? null);
            }

            $peinture = new Peinture();
            $em->persist($peinture);
            $peinture->setChantier($chantier);
            if(isset($artisants['Peinture'])){
                $artisants['Peinture'] = $artisanRepository->find($artisants['Peinture']);
                $peinture->setArtisan($artisants['Peinture'] ?? null);
            }

            $permeabilite = new Permeabilite();
            $em->persist($permeabilite);
            $permeabilite->setChantier($chantier);
            if(isset($artisants['Permeabilite'])){
                $artisants['Permeabilite'] = $artisanRepository->find($artisants['Permeabilite']);
                $permeabilite->setArtisan($artisants['Permeabilite'] ?? null);
            }

            $placo = new Placo();
            $em->persist($placo);
            $placo->setChantier($chantier);
            if(isset($artisants['Placo'])){
                $artisants['Placo'] = $artisanRepository->find($artisants['Placo']);
                $placo->setArtisan($artisants['Placo'] ?? null);
            }

            $plomberieElec = new PlomberieElec();
            $em->persist($plomberieElec);
            $plomberieElec->setChantier($chantier);
            if(isset($artisants['PlomberieElec'])){
                $artisants['PlomberieElec'] = $artisanRepository->find($artisants['PlomberieElec']);
                $plomberieElec->setArtisan($artisants['PlomberieElec'] ?? null);
            }

            $terrassement = new Terrassement();
            $em->persist($terrassement);
            $terrassement->setChantier($chantier);
            if(isset($artisants['Terrassement'])){
                $artisants['Terrassement'] = $artisanRepository->find($artisants['Terrassement']);
                $terrassement->setArtisan($artisants['Terrassement'] ?? null);
            }

            $zinguerie = new Zinguerie();
            $em->persist($zinguerie);
            $zinguerie->setChantier($chantier);
            if(isset($artisants['Zingurie'])){
                $artisants['Zingurie'] = $artisanRepository->find($artisants['Zingurie']);
                $zinguerie->setArtisan($artisants['Zingurie'] ?? null);
            }

            $entretientPAC = new EntretientPAC();
            $em->persist($entretientPAC);
            $entretientPAC->setChantier($chantier);
            if(isset($artisants['EntretientPAC'])){
                $artisants['EntretientPAC'] = $artisanRepository->find($artisants['EntretientPAC']);
                $entretientPAC->setArtisan($artisants['EntretientPAC'] ?? null);
            }

            $dateDeb = $chantier->getDateDeb();
            $dateDeb_week = $dateDeb->format('W');

            foreach (self::ordre_chantier as $key => $value) {
                if (($value['key']=="EtancheiteIntervention" and !isset($artisants['Etancheite'])) or
                    ($value['key']=="LaineSouffleIntervention" and !isset($artisants['LaineSouffle'])) or
                    ($value['key']=="PoseEscalier" and !isset($artisants['Escalier'])) or
                    ($value['key']=="Cloison" and  !isset($artisants['Carrelage']))) {
                    $etape = self::num_semaine[$value['key']];
                    $chantier->{'set' . $value['key']}(($etape + $dateDeb_week > 52) ? ($etape + $dateDeb_week) % 52 : $etape + $dateDeb_week);
                    $chantier->{'set' . $value['value']}('NON');
                    continue;
                }else{
                    $etape = self::num_semaine[$value['key']];
                    $chantier->{'set' . $value['key']}(($etape + $dateDeb_week > 52) ? ($etape + $dateDeb_week) % 52 : $etape + $dateDeb_week);
                    $chantier->{'set' . $value['value']}('PAS FAIT');                    
                }
            }

            $em->flush();

            return $this->redirectToRoute('app_chantier');
        }

        return $this->render('chantier/new.html.twig', [
            'form' => $form->createView(),
            'cdts' => $cdt
        ]);
    }

    #[Route('/chantier/show/{id}', name: 'app_chantier_show')]
    public function show(Chantier $chantier): Response
    {
        return $this->render('chantier/show.html.twig', [
            'chantier' => $chantier,
        ]);
    }


    #[Route('/chantier/getEtat', name: 'app_chantier_getEtat')]
    public function getEtat(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $chantierId = $request->request->get('chantierId');
        $metier = ucfirst($request->request->get('metier'));

        $chantier = $em->getRepository(Chantier::class)->find($chantierId);

        if (!$chantier) {
            return new JsonResponse(['error' => 'Chantier not found'], 404);
        }

        $metierClass = "App\Entity\\" . $metier;
        $metierEntity = $chantier->{'get' . $metier}();
        if ($metierEntity == null) {
            $metierEntity = new $metierClass();
            $em->persist($metierEntity);
            $metierEntity->setChantier($chantier);
        }


        $etapes = $metierEntity->getEtapes();

        return new JsonResponse([
            'etapes' => $etapes
        ]);

    }

    #[Route('/chantier/saveEtat', name: 'app_chantier_saveEtat')]
    public function saveEtat(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $chantierId = $request->request->get('chantierId');
        $metier = ucfirst($request->request->get('metier'));
        $steps = $request->request->all()['steps'];

        $chantier = $em->getRepository(Chantier::class)->find($chantierId);

        if (!$chantier) {
            return new JsonResponse(['error' => 'Chantier not found'], 404);
        }

        $metierClass = "App\Entity\\" . $metier;
        $metierEntity = $chantier->{'get' . $metier}();
        if ($metierEntity == null) {
            $metierEntity = new $metierClass();
            $em->persist($metierEntity);
            $metierEntity->setChantier($chantier);
        }

        $artisan = $em->getRepository(Artisan::class)->find($steps['Artisan']);
        $metierEntity->setArtisan($artisan);
        $metierEntity->setObservation($steps['Observation']);

        foreach ($steps as $key => $value) {
            if ($key !== 'Artisan' && $key !== 'Observation') {
                if ($value['week'] === '' || $value['week'] === null) {
                    $value['week'] = null;
                }
                $week = $metierEntity->{'set' . ucfirst($key)}($value['week']);
                $etat = $metierEntity->{'setEtat' .ucfirst($key)}($value['etat']);
            }
        }

        $em->flush();

        return new JsonResponse([
            'success' => true
        ]);
    }

    #[Route('/chantier/getChantier', name: 'app_chantier_getChantier')]
    public function getChantier(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $chantierId = $request->request->get('chantierId');

        $chantier = $em->getRepository(Chantier::class)->find($chantierId);

        if (!$chantier) {
            return new JsonResponse(['error' => 'Chantier not found'], 404);
        }

        $chantierArray = $chantier->getChantierArray();

        return new JsonResponse([
            'chantier' => $chantierArray
        ]);

    }


    #[Route('/chantier/saveChantier', name: 'app_chantier_saveChantier')]
    public function saveChantier(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $chantierId = $request->request->get('chantierId');
        $client = $request->request->get('client');
        $commune = $request->request->get('commune');
        $cdt = $request->request->get('cdt');
        $dateDeb = $request->request->get('dateDeb');
        $dateFin = $request->request->get('dateFin');
        $dateReel = $request->request->get('dateReel');

        $chantier = $em->getRepository(Chantier::class)->find($chantierId);

        if (!$chantier) {
            return new JsonResponse(['error' => 'Chantier not found'], 404);
        }

        $chantier->setClient($client);
        $chantier->setCommune($commune);
        $chantier->setCdt($cdt);
        $chantier->setDateDeb($dateDeb ? new \DateTime($dateDeb) : null);
        $chantier->setDateFin($dateFin ? new \DateTime($dateFin) : null);
        $chantier->setDateReel($dateReel ? new \DateTime($dateReel) : null);

        $em->flush();

        return new JsonResponse([
            'success' => true
        ]);
    }

    #[Route('/chantier/saveEtape', name: 'app_chantier_saveEtape')]
    public function saveEtape(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $chantierId = $request->request->get('chantierId');
        $metier = ucfirst($request->request->get('metier'));
        $etape = $request->request->get('etape');
        $etat = $request->request->get('etat');

        $chantier = $em->getRepository(Chantier::class)->find($chantierId);

        if (!$chantier) {
            return new JsonResponse(['error' => 'Chantier not found'], 404);
        }

        $metierClass = "App\Entity\\" . $metier;
        $metierEntity = $chantier->{'get' . $metier}();
        if ($metierEntity == null) {
            $metierEntity = new $metierClass();
            $em->persist($metierEntity);
            $metierEntity->setChantier($chantier);
        }

        $metierEntity->{'set' . ucfirst($etape)}($etat);

        $em->flush();

        return new JsonResponse([
            'success' => true
        ]);
    }

    #[Route('/chantier/saveWeek', name: 'app_chantier_saveWeek')]
    public function saveWeek(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $chantierId = $request->request->get('chantierId');
        $metier = ucfirst($request->request->get('metier'));
        $etape = $request->request->get('etape');
        $week = $request->request->get('week');

        $chantier = $em->getRepository(Chantier::class)->find($chantierId);

        if (!$chantier) {
            return new JsonResponse(['error' => 'Chantier not found'], 404);
        }


        $metierClass = "App\Entity\\" . $metier;
        $metierEntity = $chantier->{'get' . $metier}();
        if ($metierEntity == null) {
            $metierEntity = new $metierClass();
            $em->persist($metierEntity);
            $metierEntity->setChantier($chantier);
        }

        if ($week === '' || $week === null) {
            $week = null;
        }
        $oldWeek = $metierEntity->{'get' . ucfirst($etape)}();

        $dateCur = new \DateTime();
        if ($oldWeek != $week && !empty($oldWeek)) {
            $mouvement = sprintf(
            "%s: %s | %s => %s | %s",
            $metier,
            $etape,
            $oldWeek,
            $week,
            $dateCur->format('Y-m-d H:i:s')
            );
            $chantier->setMouvements($mouvement);
        }
        $metierEntity->{'set' . ucfirst($etape)}($week);

        $em->flush();

        $steps = [];
        $found = false;
        $diff = 0;
        foreach (self::num_semaine as $key => $value) {
            if ($found) {
                $newWeek = ($value + $diff > 52) ? ($value + $diff) % 52 : $value + $diff;
                $oldWeek = $chantier->{'get' . $key}();
            if ($newWeek != $oldWeek) {
                $steps[$key]['new'] = $newWeek;
                $steps[$key]['old'] = $oldWeek;
                $steps[$key]['displayName'] = self::names[$key];
            }
            }
            if ($key === $etape) {
            $found = true;
            $diff = $week - $value;
            }
        }

        if(count($steps) !== 0){
            return new JsonResponse([
                'success' => true,
                'steps' => $steps
            ]);
        }else{
            return new JsonResponse([
                'success' => true,
            ]);
        }
    }


    #[Route('/chantier/setArtisan', name: 'app_chantier_setArtisan')]
    public function setArtisan(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $chantierId = $request->request->get('chantierId');
        $metier = ucfirst($request->request->get('metier'));
        $artisanId = $request->request->get('artisanId');

        $chantier = $em->getRepository(Chantier::class)->find($chantierId);

        if (!$chantier) {
            return new JsonResponse(['error' => 'Chantier not found'], 404);
        }

        $metierClass = "App\Entity\\" . $metier;
        $metierEntity = $chantier->{'get' . $metier}();
        if ($metierEntity == null) {
            $metierEntity = new $metierClass();
            $em->persist($metierEntity);
            $metierEntity->setChantier($chantier);
        }

        $artisan = $em->getRepository(Artisan::class)->find($artisanId);
        $metierEntity->setArtisan($artisan);

        $em->flush();

        return new JsonResponse([
            'success' => true
        ]);
    }

    #[Route('/chantier/saveWeekArray' , name: 'app_chantier_saveWeekArray')]
    public function saveWeekArray(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $chantierId = $request->request->get('chantierId');
        $weeks = $request->request->all()['steps'];

        $chantier = $em->getRepository(Chantier::class)->find($chantierId);

        if (!$chantier) {
            return new JsonResponse(['error' => 'Chantier not found'], 404);
        }

        foreach ($weeks as $key => $value) {
            $chantier->{'set' . $value['step']}($value['value']);
            $etat = self::findetat[$value['step']];
            $chantier->{'set' . $etat}("PAS FAIT");
        }

        $em->flush();

        return new JsonResponse([
            'success' => true
        ]);
    }


    #[Route('/chantier/archiverChantier', name: 'app_chantier_archiverChantier')]
    public function archiverChantier(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $chantierId = $request->request->get('chantierId');

        $chantier = $em->getRepository(Chantier::class)->find($chantierId);

        if (!$chantier) {
            return new JsonResponse(['error' => 'Chantier not found'], 404);
        }

        $chantier->setArchive(true);

        $em->flush();

        return new JsonResponse([
            'success' => true
        ]);
    }

    #[Route('/chantier/unarchiverChantier', name: 'app_chantier_unarchiveChantier')]
    public function unarchiverChantier(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $chantierId = $request->request->get('chantierId');

        $chantier = $em->getRepository(Chantier::class)->find($chantierId);

        if (!$chantier) {
            return new JsonResponse(['error' => 'Chantier not found'], 404);
        }

        $chantier->setArchive(false);

        $em->flush();

        return new JsonResponse([
            'success' => true
        ]);
    }

    // app_chantier_deleteChantier') }}",
	// 			type: 'POST',
	// 			data: { chantierId },
    #[Route('/chantier/deleteChantier', name: 'app_chantier_deleteChantier')]
    public function deleteChantier(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $chantierId = $request->request->get('chantierId');

        $chantier = $em->getRepository(Chantier::class)->find($chantierId);

        if (!$chantier) {
            return new JsonResponse(['error' => 'Chantier not found'], 404);
        }

        $em->remove($chantier);
        $em->flush();

        return new JsonResponse([
            'success' => true
        ]);
    }

    #[Route('/chantier/statistique', name: 'app_chantier_statistique')]
    public function statistique(ChantierRepository $chantierRepository): Response
    {
        // Récupération du nombre de chantiers par conducteur
        $conducteurs = $chantierRepository->findByChantierCountByConducteur();

        // Récupération des délais moyens (entre date de début et fin contractuelle et réception)
        $delays = $chantierRepository->findByAverageDelays();

        // Récupération du nombre de chantiers par commune (avec normalisation simple)
        $communes = $chantierRepository->findByChantierCountByCommune();

        // Nouvelles statistiques
        $chantiersByYear = $chantierRepository->findByChantierCountByYear();
        $chantiersByStatus = $chantierRepository->findByChantierCountByStatus();
        $globalStats = $chantierRepository->findGlobalStats();

        // Renvoyer les données à la vue qui affichera les graphiques avec Chart.js
        return $this->render('chantier/statistique.html.twig', [
            'conducteurs' => $conducteurs,
            'delaysByYear' => $delays,
            'communes' => $communes,
            'chantiersByYear' => $chantiersByYear,
            'chantiersByStatus' => $chantiersByStatus,
            'globalStats' => $globalStats,
        ]);
    }

    // print route take filters in post 
    #[Route('/chantier/print', name: 'app_chantier_print')]
    public function print(
        ChantierRepository $chantierRepository,
        ArtisanRepository $artisanRepository
    ): Response
    {
        $artisans = $artisanRepository->findAll();
        $chantiers = $chantierRepository->findBy([], ['id' => 'DESC']);
        $cdt = $chantierRepository->findAllCDTDistinct();

        $artisans_optgroup = [];
        foreach ($artisans as $artisan) {
            if (!array_key_exists($artisan->getType(), $artisans_optgroup)) {
                $artisans_optgroup[$artisan->getType()] = [];
            }
            $artisans_optgroup[$artisan->getType()][] = $artisan;
        }
        foreach ($artisans_optgroup as $key => $value) {
            usort($artisans_optgroup[$key], function($a, $b) {
                return $a->getNom() <=> $b->getNom();
            });
        }

        return $this->render('chantier/print.html.twig', [
            'chantiers' => $chantiers,
            'artisans' => $artisans,
            'artisans_optgroup' => $artisans_optgroup,
            'cdts' => $cdt
        ]);
    }



}
