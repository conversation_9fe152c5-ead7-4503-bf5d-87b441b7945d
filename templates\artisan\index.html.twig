{% extends 'base.html.twig' %}

{% block title %}Artisans{% endblock %}

{% block body %}
{% set categories = {
    'Terrassement': '#996633',
    'Maconnerie': '#C00000',
    'PlomberieElec': '#00B0F0',
    'Charpente': '#009900',
    'Etancheite': '#008000',
    'Zinguerie': '#8497B0',
    'Menuiserie': '#000000',
    'Placo': '#CC99FF',
    'Chappe': '#00CC99',
    'EntretientPAC': 'rgb(101, 61, 13)',
    'Enduit': '#FFC000',
    'LaineSouffle': '#A50021',
    'Carrelage': '#ED7D31',
    'Escalier': '#A5A5A5',
    'Finition': '#3F888F',
    'Peinture': '#E4C6C9',
    'Permeabilite': '#9966FF'
} %}

<div class="container mt-3">
    <div class="d-flex justify-content-between align-items-center">
        <h2 class="fw-bold">Artisans</h2>
        <div class="d-flex gap-3 align-items-center">
            <select class="selectpicker" id="filtre-type" data-live-search="true" data-style="border btn" title="Filtrer type" >
                <option value="ALL" selected>Tous</option>
                <option selected value="Terrassement" data-content="<span class='badge' style='background: #996633;color: #996633'>-</span> Terrassement">Terrassement</option>
                <option selected value="Maconnerie" data-content="<span class='badge' style='background: #C00000;color: #C00000'>-</span> Maçonnerie">Maçonnerie</option>
                <option selected value="PlomberieElec" data-content="<span class='badge' style='background: #00B0F0;color: #00B0F0'>-</span> Plomberie Élec">Plomberie Élec</option>
                <option selected value="Charpente" data-content="<span class='badge' style='background: #009900;color: #009900'>-</span> Charpente">Charpente</option>
                <option selected value="Etancheite" data-content="<span class='badge' style='background: #008000;color: #008000'>-</span> Étanchéité">Étanchéité</option>
                <option selected value="Zinguerie" data-content="<span class='badge' style='background: #8497B0;color: #8497B0'>-</span> Zinguerie">Zinguerie</option>
                <option selected value="Menuiserie" data-content="<span class='badge' style='background: #000000;color: #000000'>-</span> Menuiserie">Menuiserie</option>
                <option selected value="Placo" data-content="<span class='badge' style='background: #CC99FF;color: #CC99FF'>-</span> Placo">Placo</option>
                <option selected value="Chappe" data-content="<span class='badge' style='background: #00CC99;color: #00CC99'>-</span> Chappe">Chappe</option>
                <option selected value="EntretientPAC" data-content="<span class='badge' style='background: rgb(101, 61, 13);color: rgb(101, 61, 13)'>-</span> Entretient PAC">Entretient PAC</option>
                <option selected value="Enduit" data-content="<span class='badge' style='background: #FFC000;color: #FFC000'>-</span> Enduit">Enduit</option>
                <option selected value="LaineSouffle" data-content="<span class='badge' style='background: #A50021;color: #A50021'>-</span> Laine Soufflée">Laine Soufflée</option>
                <option selected value="Carrelage" data-content="<span class='badge' style='background: #ED7D31;color: #ED7D31'>-</span> Carrelage">Carrelage</option>
                <option selected value="Escalier" data-content="<span class='badge' style='background: #A5A5A5;color: #A5A5A5'>-</span> Escalier">Escalier</option>
                <option selected value="Finition" data-content="<span class='badge' style='background: #3F888F;color: #3F888F'>-</span> Finition">Finition</option>
                <option selected value="Peinture" data-content="<span class='badge' style='background: #E4C6C9;color: #E4C6C9'>-</span> Peinture">Peinture</option>
                <option selected value="Permeabilite" data-content="<span class='badge' style='background: #9966FF;color: #9966FF'>-</span> Perméabilité">Perméabilité</option>
            </select>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addArtisanModal"><i class="bi bi-plus"></i> Ajouter</button>
        </div>
    </div>
    {% for type, artisans in groupedArtisans %}
    <div type="{{ type }}" class="div-type">
        {% if loop.index == 1 %}
            <hr class="mt-1">
        {% else %}
            <hr>
        {% endif %}
        <h3>
            <span class="badge" style="background: {{ categories[type] }}; color: {{ categories[type] }}"> </span>
            {% if type == 'Maconnerie' %}
                Maçonnerie
            {% elseif type == 'PlomberieElec' %}
                Plomberie Électricité Chauffage
            {% elseif type == 'Etancheite' %}
                Étanchéité
            {% elseif type == 'LaineSouffle' %}
                Laine Soufflée
            {% elseif type == 'Permeabilite' %}
                Perméabilité
            {% else %}
                {{ type }}
            {% endif %}
        </h3>
        <div class="d-flex flex-wrap">
            {% for artisan in artisans %}
                <div class="card mb-3 me-3 shadow border-0">
                    <div class="card-body d-flex gap-3 align-items-center" id-artisan="{{ artisan.id }}">
                        <p class="card-title mb-0">{{ artisan.nom }}</p>
                        <input type="text" style="display:none" value="{{ artisan.nom }}" class="form-control input-card form-control-sm">
                        <div class="sep-line" style="border-left: 1px solid #000; height: 80%;display:none"></div>
                        <button class="btn btn-sm btn-primary edit-btn" style="display:none"><i class="bi bi-pencil"></i></button>
                        <button class="btn btn-sm btn-danger delete-btn" style="display:none"><i class="bi bi-trash"></i></button>
                        <button class="btn btn-sm btn-danger back-btn" style="display:none"><i class="bi bi-x"></i></button>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
    {% endfor %}
</div>

<div class="modal fade" id="addArtisanModal" tabindex="-1" aria-labelledby="addArtisanModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addArtisanModalLabel">Ajouter un artisan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ path('add_artisan') }}" method="post">
                <div class="modal-body" style="max-height: 500px;">
                    <div class="mb-3">
                        <label for="nom" class="form-label fw-500">Nom</label>
                        <input type="text" class="form-control" id="nom" name="nom" required>
                    </div>
                    <div class="mb-3">
                        <label for="type" class="form-label fw-500">Type</label>
                        <select class="selectpicker" id="type" name="type" required data-live-search="true" data-style="border btn" data-width="100%" title="Choisir un type">
                            {% for type, artisans in groupedArtisans %}
                                <option value="{{ type }}">{{ type }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="submit" class="btn btn-primary">Ajouter</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('.card').on('click', function() {
            if (!$(this).find('.edit-btn').is(':visible')) {
                $('.sep-line').hide();
                $('.edit-btn').hide();
                $('.delete-btn').hide();
                $('.back-btn').hide();
                $('.input-card').hide();
                $('.card-title').show();
                $(this).find('.sep-line').show();
                $(this).find('.edit-btn').show();
                $(this).find('.delete-btn').show();
                $(this).find('.back-btn').hide();
                $(this).find('.input-card').hide();
                $(this).find('p').show();
            }
        });

        $(document).on('click', function(e) {
            if (!$(e.target).closest('.card').length) {
                $('.sep-line').hide();
                $('.edit-btn').hide();
                $('.delete-btn').hide();
                $('.back-btn').hide();
                $('.input-card').hide();
                $('.card-title').show();
            }
        });

        $('.back-btn').on('click', function(e) {
            e.stopPropagation();
            let card = $(this).closest('.card');
            let input = card.find('.input-card');
            let text = card.find('p');
            let div = card.find('.sep-line');
            let deleteBtn = card.find('.delete-btn');
            let editBtn = card.find('.edit-btn');

            editBtn.hide();
            text.show();
            input.hide();
            div.hide();
            $(this).hide();
        });

        $('.edit-btn').on('click', function(e) {
            e.stopPropagation();
            if ($(this).closest('.card').find('.input-card:visible').length > 0) {
                let id = $(this).closest('.card-body').attr('id-artisan');
                let url = "{{ path('edit_artisan', {'id': '0'}) }}";
                let backBtn = $(this).closest('.card').find('.back-btn');
                let text = $(this).closest('.card').find('p');
                url = url.replace('0', id);
                let data = {
                    nom: $(this).closest('.card').find('.input-card').val()
                };
                $.ajax({
                    url: url,
                    type: 'PUT',
                    data: data,
                    success: function(response) {
                        Toast.fire({
                            icon: 'success',
                            title: 'Artisan modifié avec succès'
                        });
                        text.text(data.nom);
                        backBtn.click();
                    }
                });
            }
            let card = $(this).closest('.card');
            let input = card.find('.input-card');
            let text = card.find('p');
            let div = card.find('.sep-line');
            let backBtn = card.find('.back-btn');
            let deleteBtn = card.find('.delete-btn');

            deleteBtn.hide();
            text.hide();
            input.show();
            div.show();
            backBtn.show();
        });
    });

    $('.delete-btn').on('click', function(e) {
        e.stopPropagation();
        let id = $(this).closest('.card-body').attr('id-artisan');
        let card = $(this).closest('.card');
        let url = "{{ path('delete_artisan', {'id': '0'}) }}";
        url = url.replace('0', id);
        Swal.fire({
            title: 'Êtes-vous sûr?',
            text: "Vous ne pourrez pas revenir en arrière!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-sm btn-danger',
                cancelButton: 'btn btn-sm btn-secondary'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: url,
                    type: 'DELETE',
                    success: function(response) {
                        card.remove();
                        Toast.fire({
                            icon: 'success',
                            title: 'Artisan supprimé avec succès'
                        });
                    }
                });
            }
        });
        
    });

    $('#filtre-type').on('change', function() {
        let type = $(this).val();
        if (type !== 'ALL') {
            $('.div-type').hide();
            $(`.div-type[type="${type}"]`).show();
        } else {
            $('.div-type').show();
        }
    });
</script>

<style>
    .card .card-body {
        transition: all 0.3s;
    }

    .card:hover {
        transition: all 0.3s;
        background-color:rgb(202, 202, 202);
    }
</style>
{% endblock %}
